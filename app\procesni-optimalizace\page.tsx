"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

export default function ProcesniOptimalizacePage() {
  const router = useRouter()

  useEffect(() => {
    // Load Cal.com script
    const script = document.createElement("script")
    script.type = "text/javascript"
    script.innerHTML = `
      (function (C, A, L) {
        let p = function (a, ar) { a.q.push(ar); };
        let d = C.document;
        C.Cal = C.Cal || function () {
          let cal = C.Cal;
          let ar = arguments;
          if (!cal.loaded) {
            cal.ns = {};
            cal.q = cal.q || [];
            d.head.appendChild(d.createElement("script")).src = A;
            cal.loaded = true;
          }
          if (ar[0] === L) {
            const api = function () { p(api, arguments); };
            const namespace = ar[1];
            api.q = api.q || [];
            if(typeof namespace === "string"){
              cal.ns[namespace] = cal.ns[namespace] || api;
              p(cal.ns[namespace], ar);
              p(cal, ["initNamespace", namespace]);
            } else p(cal, ar);
            return;
          }
          p(cal, ar);
        };
      })(window, "https://app.cal.com/embed/embed.js", "init");

      Cal("init", "hovor-s-tknurture", {origin:"https://cal.com"});

      Cal.ns["hovor-s-tknurture"]("ui", {
        "cssVarsPerTheme": {
          "light": {"cal-brand": "#000000"},
          "dark": {"cal-brand": "#a91877"}
        },
        "hideEventTypeDetails": false,
        "layout": "month_view"
      });
    `
    document.head.appendChild(script)
  }, [])

  return (
    <main className="min-h-screen bg-black text-white relative overflow-hidden">
      {/* Landing page pozadí efekt */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900/50 to-black"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(233,30,99,0.1),transparent_50%)]"></div>

      <div className="relative z-10 container mx-auto px-4 py-8">
        {/* Header - landing page styl */}
        <div className="flex items-center justify-between mb-8">
          <Button
            variant="outline"
            onClick={() => router.push("/")}
            className="border-pink-500 text-pink-500 hover:bg-pink-500/20 backdrop-blur-sm"
          >
            ← Zpět na hlavní
          </Button>
          <h1 className="text-4xl font-bold">
            <span className="text-pink-500">TK</span>
            <span className="text-white ml-2">Nurture</span>
          </h1>
        </div>

        {/* Hero Section - landing page styl */}
        <div className="text-center mb-16">
          <div className="text-6xl mb-6 animate-pulse">⚡</div>
          <h1 className="text-5xl font-bold mb-6">
            <span className="text-pink-500">Procesní</span>
            <span className="text-white ml-3">Optimalizace</span>
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Analyzujeme a optimalizujeme vaše firemní procesy pro maximální výkonnost.
            Identifikujeme úzká místa, navrhujeme efektivní řešení a implementujeme zlepšení.
          </p>
        </div>

        {/* Features Grid - landing page styl */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          <div className="bg-black/60 backdrop-blur-md rounded-xl p-8 border border-pink-500/40 hover:border-pink-500/80 transition-all duration-300 hover:bg-pink-500/10 hover:scale-105 hover:shadow-xl hover:shadow-pink-500/20">
            <div className="text-4xl mb-6 bg-pink-500/20 w-16 h-16 rounded-xl flex items-center justify-center">📊</div>
            <h3 className="text-2xl font-bold mb-4 text-white">Analýza procesů</h3>
            <p className="text-gray-300 leading-relaxed">
              Detailní mapování a analýza stávajících procesů s identifikací problémových míst.
            </p>
          </div>

          <div className="bg-black/60 backdrop-blur-md rounded-xl p-8 border border-pink-500/40 hover:border-pink-500/80 transition-all duration-300 hover:bg-pink-500/10 hover:scale-105 hover:shadow-xl hover:shadow-pink-500/20">
            <div className="text-4xl mb-6 bg-pink-500/20 w-16 h-16 rounded-xl flex items-center justify-center">🔧</div>
            <h3 className="text-2xl font-bold mb-4 text-white">Optimalizace</h3>
            <p className="text-gray-300 leading-relaxed">
              Návrh a implementace zlepšení pro zvýšení efektivity a snížení nákladů.
            </p>
          </div>

          <div className="bg-black/60 backdrop-blur-md rounded-xl p-8 border border-pink-500/40 hover:border-pink-500/80 transition-all duration-300 hover:bg-pink-500/10 hover:scale-105 hover:shadow-xl hover:shadow-pink-500/20">
            <div className="text-4xl mb-6 bg-pink-500/20 w-16 h-16 rounded-xl flex items-center justify-center">📈</div>
            <h3 className="text-2xl font-bold mb-4 text-white">Měření výsledků</h3>
            <p className="text-gray-300 leading-relaxed">
              Kontinuální monitoring a měření výkonnosti s pravidelnými reporty.
            </p>
          </div>
        </div>

        {/* Process Steps - landing page styl */}
        <div className="mb-16">
          <h2 className="text-4xl font-bold text-center mb-12">
            <span className="text-white">Náš </span>
            <span className="text-pink-500">přístup</span>
          </h2>
          <div className="grid md:grid-cols-4 gap-6">
            {[
              { step: "1", title: "Analýza", desc: "Mapování současného stavu procesů" },
              { step: "2", title: "Návrh", desc: "Optimalizované řešení a strategie" },
              { step: "3", title: "Implementace", desc: "Postupné zavádění změn" },
              { step: "4", title: "Monitoring", desc: "Sledování a měření výsledků" }
            ].map((item, index) => (
              <div key={index} className="text-center bg-black/40 backdrop-blur-sm rounded-xl p-6 border border-pink-500/30">
                <div className="w-16 h-16 bg-gradient-to-r from-pink-500 to-pink-600 rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4 text-white">
                  {item.step}
                </div>
                <h3 className="font-bold mb-2 text-white text-lg">{item.title}</h3>
                <p className="text-sm text-gray-300 leading-relaxed">{item.desc}</p>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section - landing page styl */}
        <div className="text-center bg-gradient-to-r from-pink-500/15 to-pink-500/5 rounded-2xl p-12 border border-pink-500/40 backdrop-blur-md shadow-2xl shadow-pink-500/10">
          <div className="text-5xl mb-6">⚡</div>
          <h2 className="text-4xl font-bold mb-6">
            <span className="text-white">Optimalizujte své </span>
            <span className="text-pink-500">procesy</span>
          </h2>
          <p className="text-xl text-gray-300 mb-8 leading-relaxed max-w-2xl mx-auto">
            Získejte bezplatnou analýzu vašich procesů a návrh optimalizace.
            Zvyšte efektivitu a snižte náklady díky našim řešením.
          </p>
          <div className="flex gap-6 justify-center">
            <Button
              className="bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 text-white px-10 py-4 text-lg font-bold transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-pink-500/30 rounded-xl"
              data-cal-link="tknurture/hovor-s-tknurture"
              data-cal-namespace="hovor-s-tknurture"
              data-cal-config='{"layout":"month_view"}'
            >
              Rezervovat konzultaci
            </Button>
            <Button
              variant="outline"
              className="border-2 border-pink-500 text-pink-500 hover:bg-pink-500/20 px-10 py-4 text-lg backdrop-blur-sm transition-all duration-300 hover:scale-105 rounded-xl"
              onClick={() => router.push("/")}
            >
              Další služby
            </Button>
          </div>
        </div>
      </div>
    </main>
  )
}
