"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

export default function ProcesniOptimalizacePage() {
  const router = useRouter()

  useEffect(() => {
    // Load Cal.com script
    const script = document.createElement("script")
    script.type = "text/javascript"
    script.innerHTML = `
      (function (C, A, L) {
        let p = function (a, ar) { a.q.push(ar); };
        let d = C.document;
        C.Cal = C.Cal || function () {
          let cal = C.Cal;
          let ar = arguments;
          if (!cal.loaded) {
            cal.ns = {};
            cal.q = cal.q || [];
            d.head.appendChild(d.createElement("script")).src = A;
            cal.loaded = true;
          }
          if (ar[0] === L) {
            const api = function () { p(api, arguments); };
            const namespace = ar[1];
            api.q = api.q || [];
            if(typeof namespace === "string"){
              cal.ns[namespace] = cal.ns[namespace] || api;
              p(cal.ns[namespace], ar);
              p(cal, ["initNamespace", namespace]);
            } else p(cal, ar);
            return;
          }
          p(cal, ar);
        };
      })(window, "https://app.cal.com/embed/embed.js", "init");

      Cal("init", "hovor-s-tknurture", {origin:"https://cal.com"});

      Cal.ns["hovor-s-tknurture"]("ui", {
        "cssVarsPerTheme": {
          "light": {"cal-brand": "#000000"},
          "dark": {"cal-brand": "#a91877"}
        },
        "hideEventTypeDetails": false,
        "layout": "month_view"
      });
    `
    document.head.appendChild(script)
  }, [])

  return (
    <main className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black text-white">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <Button
            variant="outline"
            onClick={() => router.push("/")}
            className="border-blue-500 text-blue-500 hover:bg-blue-900/20"
          >
            ← Zpět na hlavní
          </Button>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-pink-500 to-purple-600 bg-clip-text text-transparent">
            TK Nurture
          </h1>
        </div>

        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className="text-6xl mb-6">⚡</div>
          <h1 className="text-5xl font-bold mb-6 bg-gradient-to-r from-blue-500 to-cyan-500 bg-clip-text text-transparent">
            Procesní Optimalizace
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Analyzujeme a optimalizujeme vaše firemní procesy pro maximální výkonnost. 
            Identifikujeme úzká místa, navrhujeme efektivní řešení a implementujeme zlepšení.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-blue-500/20">
            <div className="text-3xl mb-4">📊</div>
            <h3 className="text-xl font-bold mb-3 text-blue-400">Analýza procesů</h3>
            <p className="text-gray-300">
              Detailní mapování a analýza stávajících procesů s identifikací problémových míst.
            </p>
          </div>

          <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-cyan-500/20">
            <div className="text-3xl mb-4">🔧</div>
            <h3 className="text-xl font-bold mb-3 text-cyan-400">Optimalizace</h3>
            <p className="text-gray-300">
              Návrh a implementace zlepšení pro zvýšení efektivity a snížení nákladů.
            </p>
          </div>

          <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-purple-500/20">
            <div className="text-3xl mb-4">📈</div>
            <h3 className="text-xl font-bold mb-3 text-purple-400">Měření výsledků</h3>
            <p className="text-gray-300">
              Kontinuální monitoring a měření výkonnosti s pravidelnými reporty.
            </p>
          </div>
        </div>

        {/* Process Steps */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-12">Náš přístup</h2>
          <div className="grid md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">1</div>
              <h3 className="font-bold mb-2">Analýza</h3>
              <p className="text-sm text-gray-400">Mapování současného stavu</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-cyan-500 rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">2</div>
              <h3 className="font-bold mb-2">Návrh</h3>
              <p className="text-sm text-gray-400">Optimalizované řešení</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">3</div>
              <h3 className="font-bold mb-2">Implementace</h3>
              <p className="text-sm text-gray-400">Postupné zavádění změn</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-pink-500 rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">4</div>
              <h3 className="font-bold mb-2">Monitoring</h3>
              <p className="text-sm text-gray-400">Sledování výsledků</p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center bg-gradient-to-r from-blue-500/10 to-cyan-500/10 rounded-lg p-8 border border-blue-500/30">
          <h2 className="text-3xl font-bold mb-4">Optimalizujte své procesy</h2>
          <p className="text-gray-300 mb-6">
            Získejte bezplatnou analýzu vašich procesů a návrh optimalizace.
          </p>
          <div className="flex gap-4 justify-center">
            <Button
              className="bg-blue-500 hover:bg-blue-600 text-white px-8 py-3"
              data-cal-link="tknurture/hovor-s-tknurture"
              data-cal-namespace="hovor-s-tknurture"
              data-cal-config='{"layout":"month_view"}'
            >
              Rezervovat konzultaci
            </Button>
            <Button
              variant="outline"
              className="border-blue-500 text-blue-500 hover:bg-blue-900/20 px-8 py-3"
              onClick={() => router.push("/")}
            >
              Další služby
            </Button>
          </div>
        </div>
      </div>
    </main>
  )
}
