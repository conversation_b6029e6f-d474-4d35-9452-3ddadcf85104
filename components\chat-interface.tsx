"use client"

import type React from "react"
import { useState, useRef, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Send, MessageCircle, Maximize2, Minimize2 } from "lucide-react"

export function ChatInterface() {
  const [message, setMessage] = useState("")
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: "Ahoj! Jsem AI asistent TK Nurture. Jak vám mohu pomoci s RPA automatizací?",
      sender: "ai",
      timestamp: new Date(),
    },
  ])
  const [isTyping, setIsTyping] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const handleSendMessage = async () => {
    if (!message.trim()) return

    const userMessage = {
      id: messages.length + 1,
      text: message,
      sender: "user" as const,
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, userMessage])
    const currentMessage = message
    setMessage("")
    setIsTyping(true)

    try {
      // Odeslání zprávy na webhook
      const response = await fetch("https://hook.eu2.make.com/t3aj88mp5ewys4349twcz582gxwla916", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message: currentMessage,
          timestamp: new Date().toISOString(),
          sessionId: `session_${Date.now()}`,
          userId: `user_${Date.now()}`,
        }),
      })

      if (response.ok) {
        // Získání odpovědi z webhooku
        const responseText = await response.text()
        console.log("Raw webhook response:", responseText)

        let aiResponseText = "Děkuji za vaši zprávu. Momentálně zpracovávám vaši žádost."

        try {
          // Pokus o parsování JSON odpovědi
          const aiResponseData = JSON.parse(responseText)
          console.log("Parsed webhook response:", aiResponseData)

          // Různé možné formáty odpovědi z webhooku
          aiResponseText =
            aiResponseData.response ||
            aiResponseData.message ||
            aiResponseData.reply ||
            aiResponseData.answer ||
            aiResponseData.text ||
            (typeof aiResponseData === "string" ? aiResponseData : JSON.stringify(aiResponseData))
        } catch (parseError) {
          // Pokud není JSON, použij raw text jako odpověď
          console.log("Response is not JSON, using as text:", responseText)
          if (responseText && responseText.trim()) {
            aiResponseText = responseText.trim()
          }
        }

        const aiResponse = {
          id: messages.length + 2,
          text: aiResponseText,
          sender: "ai" as const,
          timestamp: new Date(),
        }

        setMessages((prev) => [...prev, aiResponse])
      } else {
        console.error("Webhook response error:", response.status, response.statusText)

        // Fallback odpověď při HTTP chybě
        const errorResponse = {
          id: messages.length + 2,
          text: `Omlouvám se, došlo k chybě při komunikaci se serverem (${response.status}). Zkuste to prosím za chvíli.`,
          sender: "ai" as const,
          timestamp: new Date(),
        }

        setMessages((prev) => [...prev, errorResponse])
      }
    } catch (error) {
      console.error("Error sending message to webhook:", error)

      // Fallback odpověď při síťové chybě
      const errorResponse = {
        id: messages.length + 2,
        text: "Omlouvám se, momentálně mám technické potíže s připojením. Zkuste to prosím za chvíli.",
        sender: "ai" as const,
        timestamp: new Date(),
      }

      setMessages((prev) => [...prev, errorResponse])
    } finally {
      setIsTyping(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded)
  }

  useEffect(() => {
    // Scroll to bottom when messages change
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" })
    }
  }, [messages])

  return (
    <div className="fixed bottom-6 left-6 z-50">
      <div
        className={`rounded-3xl overflow-hidden relative transition-all duration-300 ease-in-out
        bg-gradient-to-br from-black/40 via-black/60 to-black/80 backdrop-blur-2xl
        shadow-[0_8px_32px_rgba(0,0,0,0.4),0_0_40px_rgba(233,30,99,0.15),inset_0_1px_0_rgba(255,255,255,0.1)]
        border border-pink-500/10
        before:content-[''] before:absolute before:inset-0 before:rounded-3xl before:p-[1px] 
        before:bg-gradient-to-br before:from-pink-500/30 before:via-transparent before:to-pink-500/30 
        before:-z-10 before:pointer-events-none
        after:content-[''] after:absolute after:inset-0 after:rounded-3xl
        after:bg-gradient-to-br after:from-white/5 after:via-transparent after:to-transparent
        after:pointer-events-none ${isExpanded ? "w-[700px] h-[500px]" : "w-[500px] h-80"}`}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-pink-500/10 bg-gradient-to-r from-pink-500/5 to-transparent backdrop-blur-sm">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-br from-pink-500 via-pink-600 to-pink-700 rounded-full flex items-center justify-center shadow-[0_0_15px_rgba(233,30,99,0.4)] border border-pink-400/20">
              <MessageCircle className="h-4 w-4 text-white drop-shadow-sm" />
            </div>
            <div>
              <h3 className="text-white font-medium text-sm drop-shadow-sm">TK Nurture AI</h3>
              <p className="text-pink-300 text-xs">Online</p>
            </div>
          </div>

          {/* Expand/Collapse Button */}
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 text-gray-400 hover:text-white hover:bg-pink-500/20 rounded-full transition-colors"
            onClick={toggleExpanded}
          >
            {isExpanded ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
          </Button>
        </div>

        {/* Chat Content */}
        <div className="flex flex-col h-[calc(100%-65px)]">
          {/* Messages */}
          <div className="flex-1 p-4 overflow-y-auto scrollbar-thin scrollbar-thumb-pink-500/20 scrollbar-track-transparent">
            {messages.map((msg) => (
              <div key={msg.id} className={`flex ${msg.sender === "user" ? "justify-end" : "justify-start"} mb-4`}>
                <div
                  className={`${isExpanded ? "max-w-[80%]" : "max-w-[85%]"} p-3 backdrop-blur-sm ${
                    msg.sender === "user"
                      ? "bg-gradient-to-br from-pink-500/90 via-pink-600/90 to-pink-700/90 text-white rounded-tl-2xl rounded-tr-2xl rounded-bl-2xl shadow-[0_4px_16px_rgba(233,30,99,0.25)] border border-pink-400/20"
                      : "bg-gradient-to-br from-gray-800/50 via-gray-900/60 to-black/70 text-gray-100 border border-pink-500/15 rounded-tl-2xl rounded-tr-2xl rounded-br-2xl shadow-[0_4px_16px_rgba(0,0,0,0.3)]"
                  }`}
                >
                  <p className="text-sm leading-relaxed drop-shadow-sm">{msg.text}</p>
                  <p className={`text-xs mt-2 ${msg.sender === "user" ? "text-pink-100/80" : "text-gray-400"}`}>
                    {msg.timestamp.toLocaleTimeString("cs-CZ", {
                      hour: "2-digit",
                      minute: "2-digit",
                    })}
                  </p>
                </div>
              </div>
            ))}
            {isTyping && (
              <div className="flex justify-start mb-4">
                <div className="bg-gradient-to-br from-gray-800/50 via-gray-900/60 to-black/70 text-gray-100 border border-pink-500/15 p-3 rounded-tl-2xl rounded-tr-2xl rounded-br-2xl shadow-[0_4px_16px_rgba(0,0,0,0.3)] backdrop-blur-sm">
                  <div className="flex space-x-1">
                    <div
                      className="w-2 h-2 bg-pink-500 rounded-full animate-bounce shadow-[0_0_4px_rgba(233,30,99,0.6)]"
                      style={{ animationDelay: "0ms" }}
                    ></div>
                    <div
                      className="w-2 h-2 bg-pink-500 rounded-full animate-bounce shadow-[0_0_4px_rgba(233,30,99,0.6)]"
                      style={{ animationDelay: "150ms" }}
                    ></div>
                    <div
                      className="w-2 h-2 bg-pink-500 rounded-full animate-bounce shadow-[0_0_4px_rgba(233,30,99,0.6)]"
                      style={{ animationDelay: "300ms" }}
                    ></div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div className="p-4 border-t border-pink-500/10 bg-gradient-to-r from-pink-500/5 to-transparent backdrop-blur-sm">
            <div className="flex space-x-3">
              <Input
                placeholder="Napište zprávu..."
                className="flex-1 bg-gradient-to-br from-gray-900/30 via-black/40 to-gray-900/50 border-pink-500/20 text-white placeholder:text-gray-400 focus:border-pink-500/40 rounded-xl shadow-[inset_0_2px_4px_rgba(0,0,0,0.3)] backdrop-blur-sm"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                disabled={isTyping}
              />
              <Button
                className="bg-gradient-to-br from-pink-500 via-pink-600 to-pink-700 hover:from-pink-600 hover:via-pink-700 hover:to-pink-800 text-white rounded-xl px-4 shadow-[0_4px_16px_rgba(233,30,99,0.3)] border border-pink-400/20 backdrop-blur-sm disabled:opacity-50"
                onClick={handleSendMessage}
                disabled={!message.trim() || isTyping}
              >
                <Send className="h-4 w-4 drop-shadow-sm" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
