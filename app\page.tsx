"use client"

import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { Button } from "@/components/ui/button"
import { MainScene } from "@/components/main-scene"
import { ChatInterface } from "@/components/chat-interface"

// Přidat na začátek souboru pod importy
declare global {
  interface Window {
    Cal?: any
  }
}

export default function Home() {
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [showServices, setShowServices] = useState(false)

  useEffect(() => {
    // Simulate loading assets
    const timer = setTimeout(() => {
      setLoading(false)
    }, 2000)

    return () => clearTimeout(timer)
  }, [])

  useEffect(() => {
    // Load Cal.com script when component mounts
    if (!loading) {
      // Vytvořit skript element s Cal.com kódem
      const script = document.createElement("script")
      script.type = "text/javascript"
      script.innerHTML = `
        (function (C, A, L) { 
          let p = function (a, ar) { a.q.push(ar); }; 
          let d = C.document; 
          C.Cal = C.Cal || function () { 
            let cal = C.Cal; 
            let ar = arguments; 
            if (!cal.loaded) { 
              cal.ns = {}; 
              cal.q = cal.q || []; 
              d.head.appendChild(d.createElement("script")).src = A; 
              cal.loaded = true; 
            } 
            if (ar[0] === L) { 
              const api = function () { p(api, arguments); }; 
              const namespace = ar[1]; 
              api.q = api.q || []; 
              if(typeof namespace === "string"){
                cal.ns[namespace] = cal.ns[namespace] || api;
                p(cal.ns[namespace], ar);
                p(cal, ["initNamespace", namespace]);
              } else p(cal, ar); 
              return;
            } 
            p(cal, ar); 
          }; 
        })(window, "https://app.cal.com/embed/embed.js", "init");
        
        Cal("init", "hovor-s-tknurture", {origin:"https://cal.com"});
        
        Cal.ns["hovor-s-tknurture"]("ui", {
          "cssVarsPerTheme": {
            "light": {"cal-brand": "#000000"},
            "dark": {"cal-brand": "#a91877"}
          },
          "hideEventTypeDetails": false,
          "layout": "month_view"
        });
      `
      document.head.appendChild(script)
    }
  }, [loading])

  const handleCalendarClick = () => {
    // Cal.com se automaticky aktivuje díky data atributům na tlačítku
    console.log("Calendar button clicked - Cal.com should open automatically")
  }

  const handleServicesClick = () => {
    setShowServices(!showServices)
  }

  const handleServiceClick = (route: string) => {
    router.push(route)
  }

  return (
    <main className="relative w-full h-screen overflow-hidden bg-black">
      {loading ? (
        <div className="absolute inset-0 flex flex-col items-center justify-center z-50 bg-black">
          <div className="w-12 h-12 rounded-full border-t-2 border-b-2 border-pink-500 animate-spin mb-4"></div>
          <h1 className="text-2xl font-bold text-white">
            Loading TK Nurture<span className="animate-pulse">...</span>
          </h1>
        </div>
      ) : (
        <>
          <div className="absolute inset-0 z-10">
            <MainScene showServices={showServices} onServiceClick={handleServiceClick} />
          </div>

          <div className="absolute top-4 right-4 z-20 flex gap-4 pointer-events-auto">
            <Button
              className="bg-pink-500 hover:bg-pink-600 text-white font-bold px-6 py-3 relative"
              onClick={handleServicesClick}
            >
              {showServices ? "Zpět na hlavní" : "Naše řešení"}
            </Button>
            <Button
              variant="outline"
              className="border-pink-500 text-pink-500 hover:bg-pink-900/20 px-6 py-3"
              onClick={handleCalendarClick}
              data-cal-link="tknurture/hovor-s-tknurture"
              data-cal-namespace="hovor-s-tknurture"
              data-cal-config='{"layout":"month_view"}'
            >
              Rezervace online schůzky
            </Button>
          </div>

          {/* Chat Interface */}
          <ChatInterface />
        </>
      )}
    </main>
  )
}
