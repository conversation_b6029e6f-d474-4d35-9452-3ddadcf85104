"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

export default function RPAAutomatizacePage() {
  const router = useRouter()

  useEffect(() => {
    // Load Cal.com script
    const script = document.createElement("script")
    script.type = "text/javascript"
    script.innerHTML = `
      (function (C, A, L) {
        let p = function (a, ar) { a.q.push(ar); };
        let d = C.document;
        C.Cal = C.Cal || function () {
          let cal = C.Cal;
          let ar = arguments;
          if (!cal.loaded) {
            cal.ns = {};
            cal.q = cal.q || [];
            d.head.appendChild(d.createElement("script")).src = A;
            cal.loaded = true;
          }
          if (ar[0] === L) {
            const api = function () { p(api, arguments); };
            const namespace = ar[1];
            api.q = api.q || [];
            if(typeof namespace === "string"){
              cal.ns[namespace] = cal.ns[namespace] || api;
              p(cal.ns[namespace], ar);
              p(cal, ["initNamespace", namespace]);
            } else p(cal, ar);
            return;
          }
          p(cal, ar);
        };
      })(window, "https://app.cal.com/embed/embed.js", "init");

      Cal("init", "hovor-s-tknurture", {origin:"https://cal.com"});

      Cal.ns["hovor-s-tknurture"]("ui", {
        "cssVarsPerTheme": {
          "light": {"cal-brand": "#000000"},
          "dark": {"cal-brand": "#a91877"}
        },
        "hideEventTypeDetails": false,
        "layout": "month_view"
      });
    `
    document.head.appendChild(script)
  }, [])

  return (
    <main className="min-h-screen bg-black text-white relative overflow-hidden">
      {/* Landing page pozadí efekt */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900/50 to-black"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(233,30,99,0.1),transparent_50%)]"></div>

      <div className="relative z-10 container mx-auto px-4 py-8">
        {/* Header - landing page styl */}
        <div className="flex items-center justify-between mb-8">
          <Button
            variant="outline"
            onClick={() => router.push("/")}
            className="border-pink-500 text-pink-500 hover:bg-pink-500/20 backdrop-blur-sm"
          >
            ← Zpět na hlavní
          </Button>
          <h1 className="text-4xl font-bold">
            <span className="text-pink-500">TK</span>
            <span className="text-white ml-2">Nurture</span>
          </h1>
        </div>

        {/* Hero Section - landing page styl */}
        <div className="text-center mb-16">
          <div className="text-6xl mb-6 animate-pulse">🤖</div>
          <h1 className="text-5xl font-bold mb-6">
            <span className="text-pink-500">RPA</span>
            <span className="text-white ml-3">Automatizace</span>
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Automatizujte opakující se procesy a ušetřete čas i náklady. Naše RPA řešení vám pomohou
            eliminovat manuální práci, zvýšit efektivitu a snížit chybovost ve vašich procesech.
          </p>
        </div>

        {/* Features Grid - landing page styl */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          <div className="bg-black/40 backdrop-blur-sm rounded-lg p-6 border border-pink-500/30 hover:border-pink-500/60 transition-all duration-300 hover:bg-pink-500/5">
            <div className="text-3xl mb-4">⚡</div>
            <h3 className="text-xl font-bold mb-3 text-pink-400">Rychlá implementace</h3>
            <p className="text-gray-300">
              Nasazení RPA řešení během několika týdnů s minimálním dopadem na stávající systémy.
            </p>
          </div>

          <div className="bg-black/40 backdrop-blur-sm rounded-lg p-6 border border-pink-500/30 hover:border-pink-500/60 transition-all duration-300 hover:bg-pink-500/5">
            <div className="text-3xl mb-4">💰</div>
            <h3 className="text-xl font-bold mb-3 text-pink-400">Úspora nákladů</h3>
            <p className="text-gray-300">
              Snížení provozních nákladů až o 60% díky automatizaci rutinních úkolů.
            </p>
          </div>

          <div className="bg-black/40 backdrop-blur-sm rounded-lg p-6 border border-pink-500/30 hover:border-pink-500/60 transition-all duration-300 hover:bg-pink-500/5">
            <div className="text-3xl mb-4">🎯</div>
            <h3 className="text-xl font-bold mb-3 text-pink-400">Přesnost</h3>
            <p className="text-gray-300">
              Eliminace lidských chyb a zajištění konzistentního výkonu 24/7.
            </p>
          </div>
        </div>

        {/* CTA Section - landing page styl */}
        <div className="text-center bg-gradient-to-r from-pink-500/10 to-pink-500/5 rounded-lg p-8 border border-pink-500/30 backdrop-blur-sm">
          <h2 className="text-3xl font-bold mb-4">
            <span className="text-white">Začněte s </span>
            <span className="text-pink-500">RPA automatizací</span>
            <span className="text-white"> ještě dnes</span>
          </h2>
          <p className="text-gray-300 mb-6 leading-relaxed">
            Kontaktujte nás pro bezplatnou konzultaci a analýzu vašich procesů.
          </p>
          <div className="flex gap-4 justify-center">
            <Button
              className="bg-pink-500 hover:bg-pink-600 text-white px-8 py-3 font-bold transition-all duration-300 hover:scale-105"
              data-cal-link="tknurture/hovor-s-tknurture"
              data-cal-namespace="hovor-s-tknurture"
              data-cal-config='{"layout":"month_view"}'
            >
              Rezervovat konzultaci
            </Button>
            <Button
              variant="outline"
              className="border-pink-500 text-pink-500 hover:bg-pink-500/20 px-8 py-3 backdrop-blur-sm transition-all duration-300"
              onClick={() => router.push("/")}
            >
              Další služby
            </Button>
          </div>
        </div>
      </div>
    </main>
  )
}
