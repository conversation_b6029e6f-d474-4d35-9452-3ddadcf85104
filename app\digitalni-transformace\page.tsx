"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { useEffect } from "react"

export default function DigitalniTransformacePage() {
  const router = useRouter()

  useEffect(() => {
    // Load Cal.com script
    const script = document.createElement("script")
    script.type = "text/javascript"
    script.innerHTML = `
      (function (C, A, L) {
        let p = function (a, ar) { a.q.push(ar); };
        let d = C.document;
        C.Cal = C.Cal || function () {
          let cal = C.Cal;
          let ar = arguments;
          if (!cal.loaded) {
            cal.ns = {};
            cal.q = cal.q || [];
            d.head.appendChild(d.createElement("script")).src = A;
            cal.loaded = true;
          }
          if (ar[0] === L) {
            const api = function () { p(api, arguments); };
            const namespace = ar[1];
            api.q = api.q || [];
            if(typeof namespace === "string"){
              cal.ns[namespace] = cal.ns[namespace] || api;
              p(cal.ns[namespace], ar);
              p(cal, ["initNamespace", namespace]);
            } else p(cal, ar);
            return;
          }
          p(cal, ar);
        };
      })(window, "https://app.cal.com/embed/embed.js", "init");

      Cal("init", "hovor-s-tknurture", {origin:"https://cal.com"});

      Cal.ns["hovor-s-tknurture"]("ui", {
        "cssVarsPerTheme": {
          "light": {"cal-brand": "#000000"},
          "dark": {"cal-brand": "#a91877"}
        },
        "hideEventTypeDetails": false,
        "layout": "month_view"
      });
    `
    document.head.appendChild(script)
  }, [])

  return (
    <main className="min-h-screen bg-black text-white relative overflow-hidden">
      {/* Landing page pozadí efekt */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900/50 to-black"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(233,30,99,0.1),transparent_50%)]"></div>

      <div className="relative z-10 container mx-auto px-4 py-8">
        {/* Header - landing page styl */}
        <div className="flex items-center justify-between mb-8">
          <Button
            variant="outline"
            onClick={() => router.push("/")}
            className="border-pink-500 text-pink-500 hover:bg-pink-500/20 backdrop-blur-sm"
          >
            ← Zpět na hlavní
          </Button>
          <h1 className="text-4xl font-bold">
            <span className="text-pink-500">TK</span>
            <span className="text-white ml-2">Nurture</span>
          </h1>
        </div>

        {/* Hero Section - landing page styl */}
        <div className="text-center mb-16">
          <div className="text-6xl mb-6 animate-pulse">🚀</div>
          <h1 className="text-5xl font-bold mb-6">
            <span className="text-pink-500">Digitální</span>
            <span className="text-white ml-3">Transformace</span>
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Komplexní digitalizace vašeho podnikání s moderními technologiemi.
            Připravíme vás na budoucnost digitálního světa a pomůžeme implementovat inovativní řešení.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-green-500/20">
            <div className="text-3xl mb-4">☁️</div>
            <h3 className="text-xl font-bold mb-3 text-green-400">Cloud migrace</h3>
            <p className="text-gray-300">
              Bezpečný přechod do cloudu s optimalizací nákladů a zvýšením flexibility.
            </p>
          </div>

          <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-emerald-500/20">
            <div className="text-3xl mb-4">🤖</div>
            <h3 className="text-xl font-bold mb-3 text-emerald-400">AI & ML řešení</h3>
            <p className="text-gray-300">
              Implementace umělé inteligence a strojového učení pro konkurenční výhodu.
            </p>
          </div>

          <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg p-6 border border-teal-500/20">
            <div className="text-3xl mb-4">📱</div>
            <h3 className="text-xl font-bold mb-3 text-teal-400">Mobilní řešení</h3>
            <p className="text-gray-300">
              Vývoj mobilních aplikací a responzivních webových řešení.
            </p>
          </div>
        </div>

        {/* Technology Stack */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-12">Technologie, které používáme</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
            {[
              { name: "React", icon: "⚛️" },
              { name: "Node.js", icon: "🟢" },
              { name: "Python", icon: "🐍" },
              { name: "AWS", icon: "☁️" },
              { name: "Docker", icon: "🐳" },
              { name: "AI/ML", icon: "🧠" }
            ].map((tech, index) => (
              <div key={index} className="text-center p-4 bg-gray-800/30 rounded-lg border border-gray-700">
                <div className="text-3xl mb-2">{tech.icon}</div>
                <p className="text-sm font-medium">{tech.name}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Benefits */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-12">Výhody digitální transformace</h2>
          <div className="grid md:grid-cols-2 gap-8">
            <div className="space-y-4">
              <div className="flex items-start gap-4">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-sm font-bold">✓</div>
                <div>
                  <h3 className="font-bold mb-1">Zvýšená efektivita</h3>
                  <p className="text-gray-400 text-sm">Automatizace procesů a optimalizace workflow</p>
                </div>
              </div>
              <div className="flex items-start gap-4">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-sm font-bold">✓</div>
                <div>
                  <h3 className="font-bold mb-1">Lepší zákaznická zkušenost</h3>
                  <p className="text-gray-400 text-sm">Moderní rozhraní a rychlejší služby</p>
                </div>
              </div>
              <div className="flex items-start gap-4">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-sm font-bold">✓</div>
                <div>
                  <h3 className="font-bold mb-1">Škálovatelnost</h3>
                  <p className="text-gray-400 text-sm">Flexibilní řešení rostoucí s vaším podnikáním</p>
                </div>
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex items-start gap-4">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-sm font-bold">✓</div>
                <div>
                  <h3 className="font-bold mb-1">Konkurenční výhoda</h3>
                  <p className="text-gray-400 text-sm">Moderní technologie pro lepší pozici na trhu</p>
                </div>
              </div>
              <div className="flex items-start gap-4">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-sm font-bold">✓</div>
                <div>
                  <h3 className="font-bold mb-1">Úspora nákladů</h3>
                  <p className="text-gray-400 text-sm">Dlouhodobé snížení provozních výdajů</p>
                </div>
              </div>
              <div className="flex items-start gap-4">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-sm font-bold">✓</div>
                <div>
                  <h3 className="font-bold mb-1">Bezpečnost</h3>
                  <p className="text-gray-400 text-sm">Moderní zabezpečení a ochrana dat</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-lg p-8 border border-green-500/30">
          <h2 className="text-3xl font-bold mb-4">Začněte svou digitální transformaci</h2>
          <p className="text-gray-300 mb-6">
            Konzultujte s námi strategii digitalizace vašeho podnikání.
          </p>
          <div className="flex gap-4 justify-center">
            <Button
              className="bg-green-500 hover:bg-green-600 text-white px-8 py-3"
              data-cal-link="tknurture/hovor-s-tknurture"
              data-cal-namespace="hovor-s-tknurture"
              data-cal-config='{"layout":"month_view"}'
            >
              Rezervovat konzultaci
            </Button>
            <Button
              variant="outline"
              className="border-green-500 text-green-500 hover:bg-green-900/20 px-8 py-3"
              onClick={() => router.push("/")}
            >
              Další služby
            </Button>
          </div>
        </div>
      </div>
    </main>
  )
}
