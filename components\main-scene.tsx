"use client"

import { useRef, useState, useEffect, useMemo } from "react"
import { Canvas, useFrame } from "@react-three/fiber"
import { Environment, OrbitControls, Float, Text3D, Sphere, MeshDistortMaterial, Html } from "@react-three/drei"
import type { Group } from "three"

interface MainSceneProps {
  showServices: boolean
  onServiceClick: (route: string) => void
}

export function MainScene({ showServices, onServiceClick }: MainSceneProps) {
  return (
    <Canvas
      camera={{ position: [0, 0, 15], fov: 60 }}
      performance={{ min: 0.5 }}
      dpr={[1, 2]}
      gl={{
        antialias: true,
        alpha: false,
        powerPreference: "high-performance",
      }}
    >
      <color attach="background" args={["#050505"]} />
      <fog attach="fog" args={["#050505", 10, 40]} />
      <ambientLight intensity={0.4} />
      <directionalLight position={[10, 10, 5]} intensity={0.8} />
      <directionalLight position={[-5, 5, 5]} intensity={0.3} color="#e91e63" />
      <Environment preset="city" />

      <OrbitControls
        enableZoom={false}
        enablePan={false}
        rotateSpeed={0.4}
        autoRotate
        autoRotateSpeed={0.3}
        minPolarAngle={Math.PI / 2.5}
        maxPolarAngle={Math.PI / 1.5}
        enableDamping
        dampingFactor={0.05}
      />

      <Float speed={1.5} rotationIntensity={0.3} floatIntensity={0.8}>
        {showServices ? (
          <ServiceCards position={[0, 1, 0]} onServiceClick={onServiceClick} />
        ) : (
          <Logo position={[0, 1, 0]} />
        )}
      </Float>

      <GridPoints />
      <FloatingParticles />
      <DataFlowLines showServices={showServices} />
    </Canvas>
  )
}

function Logo({ position }: { position: [number, number, number] }) {
  const groupRef = useRef<Group>(null)

  useFrame(({ clock }) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = Math.sin(clock.getElapsedTime() * 0.2) * 0.15
    }
  })

  return (
    <group ref={groupRef} position={position}>
      <Text3D
        font="https://threejs.org/examples/fonts/helvetiker_bold.typeface.json"
        size={1.5}
        height={0.2}
        curveSegments={8}
        bevelEnabled
        bevelThickness={0.02}
        bevelSize={0.02}
        bevelOffset={0}
        bevelSegments={3}
        position={[-5, 0, 0]}
      >
        TK
        <meshStandardMaterial color="#e91e63" metalness={0.8} roughness={0.2} />
      </Text3D>

      <Text3D
        font="https://threejs.org/examples/fonts/helvetiker_bold.typeface.json"
        size={1.5}
        height={0.2}
        curveSegments={8}
        bevelEnabled
        bevelThickness={0.02}
        bevelSize={0.02}
        bevelOffset={0}
        bevelSegments={3}
        position={[-2, 0, 0]}
      >
        Nurture
        <meshStandardMaterial color="#f0f0f0" metalness={0.8} roughness={0.2} />
      </Text3D>

      <Text3D
        font="https://threejs.org/examples/fonts/helvetiker_regular.typeface.json"
        size={0.4}
        height={0.08}
        curveSegments={6}
        bevelEnabled
        bevelThickness={0.01}
        bevelSize={0.01}
        bevelOffset={0}
        bevelSegments={2}
        position={[-4.95, -1.5, 0]}
      >
        Efektivněji společně s RPA automatizacemi
        <meshStandardMaterial color="#f0f0f0" metalness={0.6} roughness={0.3} />
      </Text3D>
    </group>
  )
}

function ServiceCard({
  service,
  position,
  onServiceClick,
  index = 0,
}: {
  service: any
  position: [number, number, number]
  onServiceClick: (route: string) => void
  index?: number
}) {
  const [hovered, setHovered] = useState(false)
  const [visible, setVisible] = useState(false)
  const cardRef = useRef<Group>(null)

  // Postupné zobrazení karet
  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(true)
    }, index * 150)
    return () => clearTimeout(timer)
  }, [index])

  useFrame(({ clock }) => {
    if (cardRef.current) {
      // Smooth hover animation
      const targetScale = hovered ? 1.08 : 1
      cardRef.current.scale.lerp({ x: targetScale, y: targetScale, z: targetScale }, 0.08)

      // Subtle rotation on hover
      if (hovered) {
        cardRef.current.rotation.z = Math.sin(clock.getElapsedTime() * 3) * 0.008
      } else {
        cardRef.current.rotation.z *= 0.92
      }
      
      // Animace zobrazení
      if (!visible) {
        cardRef.current.position.y = -20
        cardRef.current.material.opacity = 0
      } else {
        cardRef.current.position.y = position[1]
        cardRef.current.material.opacity = 1
      }
    }
  })

  const handleClick = () => {
    console.log(`Clicking on service: ${service.title}, route: ${service.route}`)
    onServiceClick(service.route)
  }

  return (
    <group 
      ref={cardRef} 
      position={position}
      visible={visible}
    >
      {/* Main Card Body - Luxurious Glass Effect */}
      <mesh
        onPointerEnter={() => setHovered(true)}
        onPointerLeave={() => setHovered(false)}
        onClick={handleClick}
        position={[0, 0, 0]}
        castShadow
        receiveShadow
      >
        <boxGeometry args={[4.5, 7, 0.4]} />
        <meshPhysicalMaterial
          color={hovered ? "#0f172a" : "#1e293b"}
          metalness={0.1}
          roughness={0.1}
          transmission={0.1}
          thickness={0.5}
          clearcoat={1}
          clearcoatRoughness={0.1}
          transparent
          opacity={0.95}
        />
      </mesh>

      {/* Elegant Border Frame */}
      <mesh position={[0, 0, 0.21]} castShadow>
        <boxGeometry args={[4.6, 7.1, 0.03]} />
        <meshPhysicalMaterial
          color="#e91e63"
          emissive="#e91e63"
          emissiveIntensity={hovered ? 0.8 : 0.5}
          metalness={0.9}
          roughness={0.1}
          clearcoat={1}
          clearcoatRoughness={0.05}
        />
      </mesh>

      {/* Inner Glow Frame */}
      <mesh position={[0, 0, 0.19]}>
        <boxGeometry args={[4.3, 6.8, 0.02]} />
        <meshPhysicalMaterial
          color="#ffffff"
          emissive="#ffffff"
          emissiveIntensity={hovered ? 0.3 : 0.15}
          metalness={0.8}
          roughness={0.2}
          transparent
          opacity={0.6}
        />
      </mesh>

      {/* Gradient Background Overlay */}
      <mesh position={[0, 1, 0.18]}>
        <boxGeometry args={[4.2, 3, 0.01]} />
        <meshPhysicalMaterial
          color="#e91e63"
          transparent
          opacity={hovered ? 0.08 : 0.05}
          emissive="#e91e63"
          emissiveIntensity={0.1}
        />
      </mesh>

      {/* Service Title - Premium Typography */}
      <Text3D
        font="https://threejs.org/examples/fonts/helvetiker_bold.typeface.json"
        size={0.35}
        height={0.12}
        curveSegments={12}
        bevelEnabled
        bevelThickness={0.03}
        bevelSize={0.03}
        bevelOffset={0}
        bevelSegments={5}
        position={[-2, 2.6, 0.25]}
      >
        {service.title}
        <meshPhysicalMaterial
          color="#ffffff"
          metalness={0.1}
          roughness={0.3}
          emissive="#ffffff"
          emissiveIntensity={hovered ? 0.2 : 0.12}
          clearcoat={0.8}
          clearcoatRoughness={0.2}
        />
      </Text3D>

      {/* Service Description - Crystal Clear Text */}
      {service.lines.map((line: string, index: number) => (
        <Text3D
          key={index}
          font="https://threejs.org/examples/fonts/helvetiker_regular.typeface.json"
          size={0.16}
          height={0.04}
          curveSegments={8}
          bevelEnabled
          bevelThickness={0.012}
          bevelSize={0.012}
          bevelOffset={0}
          bevelSegments={3}
          position={[-2, 1.8 - index * 0.38, 0.25]}
        >
          {line}
          <meshPhysicalMaterial
            color="#e2e8f0"
            metalness={0.05}
            roughness={0.7}
            emissive="#e2e8f0"
            emissiveIntensity={hovered ? 0.08 : 0.05}
            clearcoat={0.3}
            clearcoatRoughness={0.4}
          />
        </Text3D>
      ))}

      {/* Premium Icon with Holographic Effect */}
      <mesh position={[0, -0.8, 0.25]} castShadow>
        <boxGeometry args={[1.2, 1.2, 0.15]} />
        <meshPhysicalMaterial
          color="#e91e63"
          emissive="#e91e63"
          emissiveIntensity={hovered ? 0.6 : 0.3}
          metalness={0.8}
          roughness={0.1}
          clearcoat={1}
          clearcoatRoughness={0.05}
          transmission={0.1}
          thickness={0.2}
        />
      </mesh>

      {/* Icon Glow Ring */}
      <mesh position={[0, -0.8, 0.27]}>
        <ringGeometry args={[0.7, 0.9, 32]} />
        <meshPhysicalMaterial
          color="#ffffff"
          emissive="#ffffff"
          emissiveIntensity={hovered ? 0.4 : 0.2}
          transparent
          opacity={0.6}
          side={2}
        />
      </mesh>

      {/* Call to Action - Luxurious Button */}
      <mesh position={[0, -2.5, 0.25]} castShadow>
        <boxGeometry args={[3.5, 0.8, 0.1]} />
        <meshPhysicalMaterial
          color={hovered ? "#e91e63" : "#1e293b"}
          emissive={hovered ? "#e91e63" : "#1e293b"}
          emissiveIntensity={hovered ? 0.3 : 0.1}
          metalness={0.2}
          roughness={0.3}
          clearcoat={0.8}
          clearcoatRoughness={0.2}
        />
      </mesh>

      {/* CTA Text */}
      <Text3D
        font="https://threejs.org/examples/fonts/helvetiker_bold.typeface.json"
        size={0.22}
        height={0.06}
        curveSegments={8}
        bevelEnabled
        bevelThickness={0.02}
        bevelSize={0.02}
        bevelOffset={0}
        bevelSegments={3}
        position={[-1.2, -2.6, 0.32]}
      >
        VÍCE INFORMACÍ
        <meshPhysicalMaterial
          color="#ffffff"
          metalness={0.1}
          roughness={0.4}
          emissive={hovered ? "#ffffff" : "#e2e8f0"}
          emissiveIntensity={hovered ? 0.4 : 0.2}
          clearcoat={0.6}
          clearcoatRoughness={0.3}
        />
      </Text3D>

      {/* Ambient Card Lighting */}
      <pointLight position={[0, 0, 2]} intensity={hovered ? 1.2 : 0.6} color="#e91e63" distance={8} decay={2} />

      {/* Side Accent Lights */}
      <pointLight position={[-3, 0, 1]} intensity={hovered ? 0.4 : 0.2} color="#ffffff" distance={4} decay={2} />
      <pointLight position={[3, 0, 1]} intensity={hovered ? 0.4 : 0.2} color="#ffffff" distance={4} decay={2} />

      {/* Reflection Plane */}
      <mesh position={[0, 0, -0.3]} rotation={[Math.PI, 0, 0]}>
        <boxGeometry args={[4.5, 7, 0.4]} />
        <meshPhysicalMaterial
          color="#1e293b"
          metalness={0.9}
          roughness={0.1}
          transparent
          opacity={0.15}
          transmission={0.05}
        />
      </mesh>

      {/* Particle Effects Around Card */}
      {hovered && (
        <>
          <mesh position={[-2.5, 2, 0.5]}>
            <sphereGeometry args={[0.05, 8, 8]} />
            <meshPhysicalMaterial
              color="#e91e63"
              emissive="#e91e63"
              emissiveIntensity={0.8}
              transparent
              opacity={0.7}
            />
          </mesh>
          <mesh position={[2.5, -2, 0.5]}>
            <sphereGeometry args={[0.05, 8, 8]} />
            <meshPhysicalMaterial
              color="#ffffff"
              emissive="#ffffff"
              emissiveIntensity={0.6}
              transparent
              opacity={0.7}
            />
          </mesh>
          <mesh position={[0, 3.8, 0.5]}>
            <sphereGeometry args={[0.04, 8, 8]} />
            <meshPhysicalMaterial
              color="#e91e63"
              emissive="#e91e63"
              emissiveIntensity={0.9}
              transparent
              opacity={0.8}
            />
          </mesh>
        </>
      )}
    </group>
  )
}

function ServiceCards({
  position,
  onServiceClick,
}: { position: [number, number, number]; onServiceClick: (route: string) => void }) {
  const groupRef = useRef<Group>(null)

  useFrame(({ clock }) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = Math.sin(clock.getElapsedTime() * 0.15) * 0.1
    }
  })

  const services = useMemo(
    () => [
      {
        id: 1,
        title: "RPA Automatizace",
        lines: [
          "Automatizujte opakující se procesy",
          "a ušetřete čas i náklady. Naše RPA",
          "řešení vám pomohou eliminovat",
          "manuální práci, zvýšit efektivitu",
          "a snížit chybovost ve vašich procesech.",
        ],
        route: "/rpa-automatizace",
        position: [-5.5, 0, 0] as [number, number, number],
      },
      {
        id: 2,
        title: "Procesní Optimalizace",
        lines: [
          "Analyzujeme a optimalizujeme vaše",
          "firemní procesy pro maximální",
          "výkonnost. Identifikujeme úzká místa,",
          "navrhujeme efektivní řešení",
          "a implementujeme zlepšení.",
        ],
        route: "/procesni-optimalizace",
        position: [0, 0, 0] as [number, number, number],
      },
      {
        id: 3,
        title: "Digitální Transformace",
        lines: [
          "Komplexní digitalizace vašeho",
          "podnikání s moderními technologiemi.",
          "Připravíme vás na budoucnost",
          "digitálního světa a pomůžeme",
          "implementovat inovativní řešení.",
        ],
        route: "/digitalni-transformace",
        position: [5.5, 0, 0] as [number, number, number],
      },
    ],
    [],
  )

  return (
    <group ref={groupRef} position={position}>
      {services.map((service, index) => (
        <Float
          key={service.id}
          speed={1.2}
          rotationIntensity={0.15}
          floatIntensity={0.4}
        >
          <ServiceCard
            service={service}
            position={service.position}
            onServiceClick={onServiceClick}
            index={index}
          />
        </Float>
      ))}

      {/* Enhanced Back to main indicator */}
      <Html
        transform
        position={[0, -5, 0]}
        style={{
          width: "500px",
          textAlign: "center",
          pointerEvents: "none",
        }}
      >
        <div className="bg-gradient-to-r from-transparent via-pink-500/20 to-transparent p-4 rounded-lg backdrop-blur-sm border border-pink-500/30">
          <p className="text-pink-300 text-sm animate-pulse font-medium drop-shadow-lg">
            Klikněte na "Zpět na hlavní" pro návrat
          </p>
        </div>
      </Html>
    </group>
  )
}

function GridPoints() {
  const pointsRef = useRef<Group>(null)
  const [points, setPoints] = useState<Array<[number, number, number]>>([])

  const pointsData = useMemo(() => {
    const gridSize = 8
    const spacing = 2.5
    const newPoints: Array<[number, number, number]> = []

    for (let x = -gridSize; x <= gridSize; x += spacing) {
      for (let z = -gridSize; z <= gridSize; z += spacing) {
        const distance = Math.sqrt(x * x + z * z)
        if (distance > 5) {
          newPoints.push([x, -3, z])
        }
      }
    }
    return newPoints
  }, [])

  useEffect(() => {
    setPoints(pointsData)
  }, [pointsData])

  useFrame(({ clock }) => {
    if (pointsRef.current) {
      pointsRef.current.rotation.y = clock.getElapsedTime() * 0.03
    }
  })

  return (
    <group ref={pointsRef}>
      {points.map((position, i) => (
        <mesh key={i} position={position}>
          <sphereGeometry args={[0.04, 8, 8]} />
          <meshStandardMaterial color="#e91e63" emissive="#e91e63" emissiveIntensity={0.4} />
        </mesh>
      ))}
    </group>
  )
}

function FloatingParticles() {
  const particlesRef = useRef<Group>(null)
  const [particles, setParticles] = useState<
    Array<{
      position: [number, number, number]
      speed: number
      size: number
      color: string
    }>
  >([])

  const particlesData = useMemo(() => {
    const count = 30
    const newParticles = []

    for (let i = 0; i < count; i++) {
      const angle = Math.random() * Math.PI * 2
      const radius = 5 + Math.random() * 8
      const x = Math.cos(angle) * radius
      const z = Math.sin(angle) * radius
      const y = (Math.random() - 0.5) * 8

      newParticles.push({
        position: [x, y, z],
        speed: 0.15 + Math.random() * 0.2,
        size: 0.04 + Math.random() * 0.06,
        color: Math.random() > 0.7 ? "#e91e63" : "#f0f0f0",
      })
    }
    return newParticles
  }, [])

  useEffect(() => {
    setParticles(particlesData)
  }, [particlesData])

  useFrame(({ clock }) => {
    if (particlesRef.current) {
      particlesRef.current.children.forEach((particle, i) => {
        const data = particles[i]
        if (data) {
          particle.position.y += data.speed * 0.015
          if (particle.position.y > 4) {
            particle.position.y = -4
          }
        }
      })
    }
  })

  return (
    <group ref={particlesRef}>
      {particles.map((particle, i) => (
        <mesh key={i} position={particle.position}>
          <sphereGeometry args={[particle.size, 6, 6]} />
          <meshStandardMaterial
            color={particle.color}
            emissive={particle.color}
            emissiveIntensity={0.4}
            transparent
            opacity={0.6}
          />
        </mesh>
      ))}
    </group>
  )
}

function DataFlowLines({ showServices }: { showServices: boolean }) {
  const linesRef = useRef<Group>(null)
  const sphereRef = useRef<any>(null)
  const [currentScale, setCurrentScale] = useState(1)
  const [currentOpacity, setCurrentOpacity] = useState(0.2)

  useFrame(({ clock }) => {
    if (linesRef.current) {
      linesRef.current.rotation.y = clock.getElapsedTime() * 0.08
    }

    // Ultra smooth implosion animation
    if (sphereRef.current) {
      const targetScale = showServices ? 0 : 1
      const targetOpacity = showServices ? 0 : 0.2

      // Exponential easing for ultra smooth animation
      const scaleEasing = 0.025
      const opacityEasing = 0.03

      const newScale = currentScale + (targetScale - currentScale) * scaleEasing
      const newOpacity = currentOpacity + (targetOpacity - currentOpacity) * opacityEasing

      setCurrentScale(newScale)
      setCurrentOpacity(newOpacity)

      // Apply smooth transformations
      sphereRef.current.scale.set(newScale, newScale, newScale)

      if (sphereRef.current.material) {
        sphereRef.current.material.opacity = newOpacity
      }

      // Hide when very small for performance
      sphereRef.current.visible = newScale > 0.005
    }
  })

  return (
    <group ref={linesRef} position={[0, 0, 0]}>
      <Sphere ref={sphereRef} args={[8, 24, 24]} position={[0, 0, 0]}>
        <MeshDistortMaterial
          color="#e91e63"
          attach="material"
          distort={0.25}
          speed={1.5}
          wireframe
          transparent
          opacity={currentOpacity}
        />
      </Sphere>
    </group>
  )
}




