"use client"

import { useRef, useState, useEffect, useMemo } from "react"
import { Canvas, useFrame } from "@react-three/fiber"
import { Environment, OrbitControls, Float, Text3D, Sphere, MeshDistortMaterial, Html } from "@react-three/drei"
import type { Group } from "three"

interface MainSceneProps {
  showServices: boolean
  onServiceClick: (route: string) => void
}

export function MainScene({ showServices, onServiceClick }: MainSceneProps) {
  return (
    <Canvas
      camera={{ position: [0, 0, 15], fov: 60 }}
      performance={{ min: 0.5 }}
      dpr={[1, 2]}
      gl={{
        antialias: true,
        alpha: false,
        powerPreference: "high-performance",
      }}
    >
      <color attach="background" args={["#050505"]} />
      <fog attach="fog" args={["#050505", 10, 40]} />
      <ambientLight intensity={0.4} />
      <directionalLight position={[10, 10, 5]} intensity={0.8} />
      <directionalLight position={[-5, 5, 5]} intensity={0.3} color="#e91e63" />
      <Environment preset="city" />

      <OrbitControls
        enableZoom={false}
        enablePan={false}
        rotateSpeed={0.4}
        autoRotate
        autoRotateSpeed={0.3}
        minPolarAngle={Math.PI / 2.5}
        maxPolarAngle={Math.PI / 1.5}
        enableDamping
        dampingFactor={0.05}
      />

      <Float speed={1.5} rotationIntensity={0.3} floatIntensity={0.8}>
        {showServices ? (
          <ServiceCards position={[0, 1, 0]} onServiceClick={onServiceClick} />
        ) : (
          <Logo position={[0, 1, 0]} />
        )}
      </Float>

      <GridPoints />
      <FloatingParticles />
      <DataFlowLines showServices={showServices} />
    </Canvas>
  )
}

function Logo({ position }: { position: [number, number, number] }) {
  const groupRef = useRef<Group>(null)

  useFrame(({ clock }) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = Math.sin(clock.getElapsedTime() * 0.2) * 0.15
    }
  })

  return (
    <group ref={groupRef} position={position}>
      <Text3D
        font="https://threejs.org/examples/fonts/helvetiker_bold.typeface.json"
        size={1.5}
        height={0.2}
        curveSegments={8}
        bevelEnabled
        bevelThickness={0.02}
        bevelSize={0.02}
        bevelOffset={0}
        bevelSegments={3}
        position={[-5, 0, 0]}
      >
        TK
        <meshStandardMaterial color="#e91e63" metalness={0.8} roughness={0.2} />
      </Text3D>

      <Text3D
        font="https://threejs.org/examples/fonts/helvetiker_bold.typeface.json"
        size={1.5}
        height={0.2}
        curveSegments={8}
        bevelEnabled
        bevelThickness={0.02}
        bevelSize={0.02}
        bevelOffset={0}
        bevelSegments={3}
        position={[-2, 0, 0]}
      >
        Nurture
        <meshStandardMaterial color="#f0f0f0" metalness={0.8} roughness={0.2} />
      </Text3D>

      <Text3D
        font="https://threejs.org/examples/fonts/helvetiker_regular.typeface.json"
        size={0.4}
        height={0.08}
        curveSegments={6}
        bevelEnabled
        bevelThickness={0.01}
        bevelSize={0.01}
        bevelOffset={0}
        bevelSegments={2}
        position={[-4.95, -1.5, 0]}
      >
        Efektivněji společně s RPA automatizacemi
        <meshStandardMaterial color="#f0f0f0" metalness={0.6} roughness={0.3} />
      </Text3D>
    </group>
  )
}

function CompactServiceCard({
  service,
  position,
  onServiceClick,
  index = 0,
}: {
  service: any
  position: [number, number, number]
  onServiceClick: (route: string) => void
  index?: number
}) {
  const [hovered, setHovered] = useState(false)
  const [visible, setVisible] = useState(false)
  const cardRef = useRef<Group>(null)

  // Animace vyjetí z tlačítka s postupným zpožděním
  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(true)
    }, index * 200 + 300) // Zpoždění pro efekt vyjetí z tlačítka
    return () => clearTimeout(timer)
  }, [index])

  useFrame(({ clock }) => {
    if (cardRef.current) {
      // Smooth hover animation
      const targetScale = hovered ? 1.15 : 1
      cardRef.current.scale.lerp({ x: targetScale, y: targetScale, z: targetScale }, 0.1)

      // Animace vyjetí z tlačítka (shora dolů)
      if (!visible) {
        cardRef.current.position.y = 8 // Začíná u tlačítka
        cardRef.current.scale.set(0.1, 0.1, 0.1) // Malá velikost
      } else {
        // Smooth transition na finální pozici
        const targetY = position[1]
        cardRef.current.position.y += (targetY - cardRef.current.position.y) * 0.08

        // Smooth scale transition
        const targetScale = hovered ? 1.15 : 1
        const currentScale = cardRef.current.scale.x
        const newScale = currentScale + (targetScale - currentScale) * 0.08
        cardRef.current.scale.set(newScale, newScale, newScale)
      }

      // Jemná rotace při hoveru
      if (hovered) {
        cardRef.current.rotation.y = Math.sin(clock.getElapsedTime() * 2) * 0.1
      } else {
        cardRef.current.rotation.y *= 0.95
      }
    }
  })

  const handleClick = () => {
    console.log(`Clicking on service: ${service.title}, route: ${service.route}`)
    onServiceClick(service.route)
  }

  return (
    <group
      ref={cardRef}
      position={position}
      visible={visible}
    >
      {/* Kompaktní karta - moderní design */}
      <mesh
        onPointerEnter={() => setHovered(true)}
        onPointerLeave={() => setHovered(false)}
        onClick={handleClick}
        position={[0, 0, 0]}
        castShadow
        receiveShadow
      >
        <boxGeometry args={[2.8, 3.5, 0.3]} />
        <meshPhysicalMaterial
          color={hovered ? "#1a1a1a" : "#2a2a2a"}
          metalness={0.2}
          roughness={0.1}
          transmission={0.05}
          thickness={0.3}
          clearcoat={0.8}
          clearcoatRoughness={0.1}
          transparent
          opacity={0.9}
        />
      </mesh>

      {/* Barevný accent border */}
      <mesh position={[0, 0, 0.16]} castShadow>
        <boxGeometry args={[2.9, 3.6, 0.02]} />
        <meshPhysicalMaterial
          color={service.color}
          emissive={service.color}
          emissiveIntensity={hovered ? 0.6 : 0.3}
          metalness={0.8}
          roughness={0.1}
          clearcoat={1}
          clearcoatRoughness={0.05}
        />
      </mesh>

      {/* Glow efekt */}
      <mesh position={[0, 0, 0.14]}>
        <boxGeometry args={[2.7, 3.4, 0.01]} />
        <meshPhysicalMaterial
          color="#ffffff"
          emissive="#ffffff"
          emissiveIntensity={hovered ? 0.2 : 0.1}
          transparent
          opacity={0.4}
        />
      </mesh>

      {/* Ikona služby */}
      <Text3D
        font="https://threejs.org/examples/fonts/helvetiker_bold.typeface.json"
        size={0.6}
        height={0.1}
        curveSegments={8}
        position={[-0.3, 0.8, 0.18]}
      >
        {service.icon}
        <meshPhysicalMaterial
          color={service.color}
          emissive={service.color}
          emissiveIntensity={hovered ? 0.4 : 0.2}
          metalness={0.1}
          roughness={0.3}
        />
      </Text3D>

      {/* Název služby - kompaktní */}
      <Text3D
        font="https://threejs.org/examples/fonts/helvetiker_bold.typeface.json"
        size={0.18}
        height={0.06}
        curveSegments={8}
        bevelEnabled
        bevelThickness={0.01}
        bevelSize={0.01}
        bevelOffset={0}
        bevelSegments={3}
        position={[-1.2, 0.1, 0.18]}
      >
        {service.title}
        <meshPhysicalMaterial
          color="#ffffff"
          metalness={0.1}
          roughness={0.3}
          emissive="#ffffff"
          emissiveIntensity={hovered ? 0.15 : 0.08}
          clearcoat={0.6}
          clearcoatRoughness={0.3}
        />
      </Text3D>

      {/* Krátký popis */}
      <Text3D
        font="https://threejs.org/examples/fonts/helvetiker_regular.typeface.json"
        size={0.12}
        height={0.03}
        curveSegments={6}
        position={[-1.2, -0.3, 0.18]}
      >
        {service.description}
        <meshPhysicalMaterial
          color="#cccccc"
          metalness={0.05}
          roughness={0.7}
          emissive="#cccccc"
          emissiveIntensity={hovered ? 0.06 : 0.03}
        />
      </Text3D>

      {/* Jednoduché CTA */}
      <mesh position={[0, -1.2, 0.18]} castShadow>
        <boxGeometry args={[2.4, 0.5, 0.08]} />
        <meshPhysicalMaterial
          color={hovered ? service.color : "#333333"}
          emissive={hovered ? service.color : "#333333"}
          emissiveIntensity={hovered ? 0.3 : 0.1}
          metalness={0.2}
          roughness={0.3}
          clearcoat={0.6}
          clearcoatRoughness={0.2}
        />
      </mesh>

      {/* CTA Text */}
      <Text3D
        font="https://threejs.org/examples/fonts/helvetiker_bold.typeface.json"
        size={0.14}
        height={0.04}
        curveSegments={6}
        position={[-0.6, -1.25, 0.22]}
      >
        VÍCE INFO
        <meshPhysicalMaterial
          color="#ffffff"
          metalness={0.1}
          roughness={0.4}
          emissive="#ffffff"
          emissiveIntensity={hovered ? 0.3 : 0.15}
        />
      </Text3D>

      {/* Jemné osvětlení karty */}
      <pointLight
        position={[0, 0, 1.5]}
        intensity={hovered ? 0.8 : 0.4}
        color={service.color}
        distance={6}
        decay={2}
      />

      {/* Hover efekt - jemné částice */}
      {hovered && (
        <>
          <mesh position={[-1, 1.5, 0.5]}>
            <sphereGeometry args={[0.03, 6, 6]} />
            <meshPhysicalMaterial
              color={service.color}
              emissive={service.color}
              emissiveIntensity={0.6}
              transparent
              opacity={0.8}
            />
          </mesh>
          <mesh position={[1, 1.5, 0.5]}>
            <sphereGeometry args={[0.03, 6, 6]} />
            <meshPhysicalMaterial
              color={service.color}
              emissive={service.color}
              emissiveIntensity={0.6}
              transparent
              opacity={0.8}
            />
          </mesh>
        </>
      )}
    </group>
  )
}

function ServiceCards({
  position,
  onServiceClick,
}: { position: [number, number, number]; onServiceClick: (route: string) => void }) {
  const groupRef = useRef<Group>(null)

  const services = useMemo(
    () => [
      {
        id: 1,
        title: "RPA Automatizace",
        description: "Automatizace procesů",
        icon: "🤖",
        route: "/rpa-automatizace",
        color: "#e91e63",
      },
      {
        id: 2,
        title: "Procesní Optimalizace",
        description: "Optimalizace workflow",
        icon: "⚡",
        route: "/procesni-optimalizace",
        color: "#3b82f6",
      },
      {
        id: 3,
        title: "Digitální Transformace",
        description: "Modernizace podnikání",
        icon: "🚀",
        route: "/digitalni-transformace",
        color: "#10b981",
      },
    ],
    [],
  )

  return (
    <group ref={groupRef} position={position}>
      {services.map((service, index) => (
        <CompactServiceCard
          key={service.id}
          service={service}
          position={[
            (index - 1) * 3.5, // Horizontální rozložení
            -2, // Pod tlačítkem
            0
          ]}
          onServiceClick={onServiceClick}
          index={index}
        />
      ))}
    </group>
  )
}

function GridPoints() {
  const pointsRef = useRef<Group>(null)
  const [points, setPoints] = useState<Array<[number, number, number]>>([])

  const pointsData = useMemo(() => {
    const gridSize = 8
    const spacing = 2.5
    const newPoints: Array<[number, number, number]> = []

    for (let x = -gridSize; x <= gridSize; x += spacing) {
      for (let z = -gridSize; z <= gridSize; z += spacing) {
        const distance = Math.sqrt(x * x + z * z)
        if (distance > 5) {
          newPoints.push([x, -3, z])
        }
      }
    }
    return newPoints
  }, [])

  useEffect(() => {
    setPoints(pointsData)
  }, [pointsData])

  useFrame(({ clock }) => {
    if (pointsRef.current) {
      pointsRef.current.rotation.y = clock.getElapsedTime() * 0.03
    }
  })

  return (
    <group ref={pointsRef}>
      {points.map((position, i) => (
        <mesh key={i} position={position}>
          <sphereGeometry args={[0.04, 8, 8]} />
          <meshStandardMaterial color="#e91e63" emissive="#e91e63" emissiveIntensity={0.4} />
        </mesh>
      ))}
    </group>
  )
}

function FloatingParticles() {
  const particlesRef = useRef<Group>(null)
  const [particles, setParticles] = useState<
    Array<{
      position: [number, number, number]
      speed: number
      size: number
      color: string
    }>
  >([])

  const particlesData = useMemo(() => {
    const count = 30
    const newParticles = []

    for (let i = 0; i < count; i++) {
      const angle = Math.random() * Math.PI * 2
      const radius = 5 + Math.random() * 8
      const x = Math.cos(angle) * radius
      const z = Math.sin(angle) * radius
      const y = (Math.random() - 0.5) * 8

      newParticles.push({
        position: [x, y, z],
        speed: 0.15 + Math.random() * 0.2,
        size: 0.04 + Math.random() * 0.06,
        color: Math.random() > 0.7 ? "#e91e63" : "#f0f0f0",
      })
    }
    return newParticles
  }, [])

  useEffect(() => {
    setParticles(particlesData)
  }, [particlesData])

  useFrame(({ clock }) => {
    if (particlesRef.current) {
      particlesRef.current.children.forEach((particle, i) => {
        const data = particles[i]
        if (data) {
          particle.position.y += data.speed * 0.015
          if (particle.position.y > 4) {
            particle.position.y = -4
          }
        }
      })
    }
  })

  return (
    <group ref={particlesRef}>
      {particles.map((particle, i) => (
        <mesh key={i} position={particle.position}>
          <sphereGeometry args={[particle.size, 6, 6]} />
          <meshStandardMaterial
            color={particle.color}
            emissive={particle.color}
            emissiveIntensity={0.4}
            transparent
            opacity={0.6}
          />
        </mesh>
      ))}
    </group>
  )
}

function DataFlowLines({ showServices }: { showServices: boolean }) {
  const linesRef = useRef<Group>(null)
  const sphereRef = useRef<any>(null)
  const [currentScale, setCurrentScale] = useState(1)
  const [currentOpacity, setCurrentOpacity] = useState(0.2)

  useFrame(({ clock }) => {
    if (linesRef.current) {
      linesRef.current.rotation.y = clock.getElapsedTime() * 0.08
    }

    // Ultra smooth implosion animation
    if (sphereRef.current) {
      const targetScale = showServices ? 0 : 1
      const targetOpacity = showServices ? 0 : 0.2

      // Exponential easing for ultra smooth animation
      const scaleEasing = 0.025
      const opacityEasing = 0.03

      const newScale = currentScale + (targetScale - currentScale) * scaleEasing
      const newOpacity = currentOpacity + (targetOpacity - currentOpacity) * opacityEasing

      setCurrentScale(newScale)
      setCurrentOpacity(newOpacity)

      // Apply smooth transformations
      sphereRef.current.scale.set(newScale, newScale, newScale)

      if (sphereRef.current.material) {
        sphereRef.current.material.opacity = newOpacity
      }

      // Hide when very small for performance
      sphereRef.current.visible = newScale > 0.005
    }
  })

  return (
    <group ref={linesRef} position={[0, 0, 0]}>
      <Sphere ref={sphereRef} args={[8, 24, 24]} position={[0, 0, 0]}>
        <MeshDistortMaterial
          color="#e91e63"
          attach="material"
          distort={0.25}
          speed={1.5}
          wireframe
          transparent
          opacity={currentOpacity}
        />
      </Sphere>
    </group>
  )
}




